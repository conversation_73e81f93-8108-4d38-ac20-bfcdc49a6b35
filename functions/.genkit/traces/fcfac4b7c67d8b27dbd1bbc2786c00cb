{"traceId": "fcfac4b7c67d8b27dbd1bbc2786c00cb", "spans": {"8eaea5aced187c7f": {"spanId": "8eaea5aced187c7f", "traceId": "fcfac4b7c67d8b27dbd1bbc2786c00cb", "parentSpanId": "04bd0f4e5c2a3041", "startTime": 1752317872153, "endTime": 1752317872357.8943, "attributes": {"transactional": false, "doc_count": 1, "otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "Batch.Commit", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "04bd0f4e5c2a3041": {"spanId": "04bd0f4e5c2a3041", "traceId": "fcfac4b7c67d8b27dbd1bbc2786c00cb", "parentSpanId": "0e3112b8a9454496", "startTime": 1752317872153, "endTime": 1752317872361.5488, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "DocumentReference.Create", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "0e3112b8a9454496": {"spanId": "0e3112b8a9454496", "traceId": "fcfac4b7c67d8b27dbd1bbc2786c00cb", "startTime": 1752317872152, "endTime": 1752317872362.2498, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "CollectionReference.Add", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "CollectionReference.Add", "startTime": 1752317872152, "endTime": 1752317872362.2498}