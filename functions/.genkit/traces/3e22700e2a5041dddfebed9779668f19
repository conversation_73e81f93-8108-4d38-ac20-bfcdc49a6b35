{"traceId": "3e22700e2a5041dddfebed9779668f19", "spans": {"92245a699211e4f2": {"spanId": "92245a699211e4f2", "traceId": "3e22700e2a5041dddfebed9779668f19", "parentSpanId": "c3e3997bfacc89c0", "startTime": 1752317869701, "endTime": 1752317869963.417, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "embedder", "genkit:name": "googleai/text-embedding-004", "genkit:path": "/{therapies<PERSON><PERSON>riever,t:action}/{googleai/text-embedding-004,t:action,s:embedder}", "genkit:input": "{\"input\":[{\"content\":[{\"text\":\"\\n      User mood score: 3/10\\n      Areas of concern: relationships, social life\\n      Additional information: \\n      Help the user find a the therapy type for their needs.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]}],\"options\":{}}", "genkit:output": "{\"embeddings\":[{\"embedding\":[0.033809822,0.016684026,-0.05508675,-0.02253149,0.002467049,0.04531735,0.07523879,-0.006737171,-0.040377583,0.034078047,-0.012203904,0.04892562,0.021527342,0.019292055,0.0026214274,-0.053719167,-0.039926276,0.058116905,-0.08236278,-0.03467149,0.016472122,0.0032819102,-0.033486795,-0.009111869,-0.029793154,-0.009730716,0.01996753,0.01615944,0.06067474,0.0027624692,-0.004764644,0.020067338,0.087179385,-0.008010077,0.0424067,0.022851015,0.008640304,0.0023917353,-0.02305504,-0.06802996,-0.04043475,0.0013209252,0.016914517,0.01135085,-0.013384434,-0.03536259,-0.036399934,-0.019747585,-0.05694494,0.012579821,-0.003803243,0.004868304,-0.035037357,0.02808633,-0.07120294,-0.009484178,0.023693677,0.002896306,0.055898815,-0.040028434,0.016393175,-0.038533825,-0.0039724903,-0.025497865,0.037183926,-0.037057605,0.046016872,0.04668271,-0.097727045,0.012530044,0.009366279,0.028769393,-0.06788423,0.02235884,-0.009355187,-0.024740621,-0.0062921927,0.0322237,0.02503107,-0.024384988,-0.034516197,-0.029309602,0.008966268,0.08535675,-0.011140935,0.016230106,-0.039051656,-0.030942362,-0.048067816,-0.043626286,0.0038272485,0.05517765,-0.010929282,-0.0091975145,0.025171258,0.019650012,-0.04104366,-0.041692317,0.002996568,0.014102435,-0.010769846,0.03175606,0.0032524967,-0.06566406,0.037924238,0.08298335,-0.015882963,-0.02066508,-0.036828693,0.07631301,0.028774844,-0.0024487858,0.058228947,-0.053110324,-0.004622423,0.05879676,-0.0033559364,-0.002074343,0.0532577,-0.06059863,-0.021328224,0.0793494,-0.031777736,0.06607903,0.091756165,-0.015575405,0.006805044,-0.033618826,0.008448639,-0.014022282,0.026090384,-0.035638776,-0.010684901,0.037483573,0.019085852,0.02474804,0.040063057,-0.040110305,0.014547955,0.044100616,-0.045239955,-0.023735791,-0.05230414,0.047525387,-0.008567553,-0.07210807,0.028000496,0.06869812,-0.019353714,0.05641937,-0.060827635,0.008956438,0.060499348,0.09417552,-0.061813917,0.0064500836,0.0053435676,-0.002931198,0.033931665,0.011626105,0.032854024,-0.041837018,-0.001256404,0.029864633,-0.055420093,-0.022227332,-0.049877178,-0.040047944,-0.0012582882,-0.03867803,-0.016923128,0.014088912,-0.060652085,-0.025287641,-0.0033064117,0.0063587367,-0.027496446,0.023589563,-0.05096495,-0.0055461465,0.080730036,0.031017143,-0.019574687,-0.08907521,-0.0068881866,0.009744388,-0.02542958,0.004133682,-0.011053963,0.0066093113,0.041218467,-0.011789517,0.023449067,-0.010857841,-0.013941354,0.06274551,0.0996027,-0.038242072,0.018530447,-0.019659288,0.011521928,0.027270569,-0.024122182,-0.02200506,-0.020650493,-0.008412997,-0.026136877,-0.024543019,-0.01702638,0.035421908,-0.050018515,-0.001993576,-0.017077582,-0.063393235,-0.0071835024,0.032857135,0.017554618,-0.054790307,0.032780692,-0.032082062,-0.008390861,0.025822107,-0.012057132,-0.015866254,0.035702128,-0.026526924,-0.09945844,-0.022756232,-0.05091994,-0.03718146,0.00046592037,0.08497158,0.0027257537,-0.030107198,0.007256985,0.051544905,0.020526966,-0.044504244,0.025831759,-0.018570628,-0.096011445,0.047961775,-0.060454436,-0.0168324,0.049033556,-0.008520372,0.07289893,0.028342674,0.0019665807,-0.0021291138,0.038161755,-0.021202382,-0.06384176,-0.023199901,-0.024122233,-0.02849948,0.028941242,-0.0049708984,0.0041913185,0.033247944,0.038163923,-0.00042037928,-0.031025462,0.014945126,-0.019585095,-0.050242126,-0.020528441,-0.0048083034,0.053153053,-0.03714837,0.0133428285,-0.017404333,-0.03563643,-0.007861675,-0.028210575,0.008686333,-0.029702816,0.077383585,-0.06974115,0.013783003,0.033610474,0.0063518262,0.05163501,0.011078485,-0.00096274714,-0.039075565,0.01280995,-0.022241365,-0.02528533,-0.078864165,0.046819188,-0.00725941,-0.03323627,-0.06631668,0.015257757,-0.026366787,0.05271154,0.060153015,0.01738729,0.00974686,-0.0012432459,-0.02076014,-0.022880822,0.06482612,-0.06476749,0.035943393,0.0045209313,0.0016659555,-0.014202498,0.025634293,0.026288563,0.008799672,-0.022369012,-0.011180391,-0.055118203,-0.015978573,-0.05634545,-0.033253595,-0.012182197,-0.027252508,0.027171886,0.037161235,-0.023308199,-0.0026879457,0.043587282,-0.00005251854,0.036785867,0.035988517,0.018696409,0.038919758,0.0146363545,-0.014369095,-0.015398528,-0.10081592,0.011917045,-0.037725404,-0.0060484274,0.06491441,0.08373397,-0.0147917345,0.04680009,0.002530132,0.053257924,-0.0048686317,0.022654347,0.013102431,-0.053385917,-0.031661645,0.02382192,0.0030925,0.081057474,0.052785676,0.010182824,0.017480318,0.00029308168,0.0024230715,0.042574897,-0.009985352,0.037993565,0.040195007,-0.012716035,0.0018768961,0.014258108,0.021371955,-0.01700599,-0.04100954,0.05213574,-0.010505022,0.009783537,0.0015386028,0.013301685,0.0012350053,0.058329407,0.00090114196,0.027213227,-0.013246502,-0.04804844,0.0316361,0.013415975,-0.029067067,-0.0015543479,-0.052758202,-0.077331014,0.024593884,-0.015740993,0.047047384,-0.043806,0.02090619,-0.020047236,0.030909196,-0.0074412283,0.058979467,-0.0034001316,0.019075368,0.01348448,-0.01585408,-0.02803708,0.039124537,-0.038436048,-0.0050637177,0.009105167,-0.025407575,0.043217827,-0.012092485,0.03236982,-0.038983256,0.07275897,-0.0215006,-0.01157087,0.06459336,-0.010242218,-0.018775813,-0.08150872,0.06918328,-0.017587904,0.028835664,0.004717692,0.014860081,0.04982879,0.01488982,0.036653038,0.03162924,0.03108055,-0.0056527434,0.027189383,-0.036331747,-0.022137202,0.005503206,-0.0106538115,-0.019397104,0.031108001,0.009215456,-0.012057461,0.019811086,-0.0037514672,0.038483933,-0.030559672,-0.057706915,0.009988946,0.03812742,0.023306638,-0.013497426,0.0038958357,-0.014896824,-0.0026414276,-0.023417804,0.027548099,0.0026802197,-0.010408132,0.009699886,0.03261451,-0.020241274,-0.004109966,-0.064816736,-0.08601841,0.037430126,-0.039868012,0.016877154,-0.01442938,0.016948624,-0.039448243,-0.016294967,0.032804415,-0.02666413,-0.018471718,-0.02202982,-0.07812245,0.03386941,-0.011075439,-0.03317921,-0.008816299,0.04259356,0.05666467,0.024064329,-0.083439395,-0.031679768,0.059119996,0.004416713,-0.00046722207,-0.019786103,-0.075324684,-0.014048124,-0.04147386,0.04334036,0.069181636,0.0053720796,-0.016446471,0.020210389,0.017181002,-0.049577616,-0.04589784,0.035525803,-0.027976977,0.008015039,0.008831036,-0.013554528,0.022350036,-0.005205953,-0.01245845,0.090663925,-0.011736015,0.034999225,-0.033840373,-0.0508696,-0.045856167,0.0060422095,-0.037565637,0.005963723,0.029339204,0.053776763,0.025192644,-0.030590635,-0.007980489,0.002601696,-0.048787653,-0.0019545772,-0.015514169,0.02554918,-0.014705061,0.04506569,0.024109472,-0.020588612,0.012001269,-0.013891802,0.011774751,-0.008807281,0.009150871,-0.0037964832,0.04470796,-0.08096593,0.0039199847,0.04484136,0.0041272957,0.014715535,0.016153222,0.08029134,0.054871082,0.031256713,0.0069868267,-0.030788563,0.039993003,0.033256736,0.017343635,0.011968904,0.013290581,-0.024020186,-0.02407656,0.10600611,0.012676555,0.041818246,-0.025205208,0.0036199538,0.020709692,-0.011374076,-0.017783077,0.02145798,0.034341548,0.03272903,0.02844886,-0.029239679,0.016577613,-0.005182519,-0.007354144,-0.04038433,-0.037036374,-0.06716419,-0.060310997,0.00030815395,-0.009726071,0.01636836,-0.024967335,-0.047969524,0.026457943,-0.037025407,0.012502385,-0.053137433,-0.031686418,-0.020352554,0.027608473,0.011029132,-0.014418057,0.033985913,0.032138754,0.002763884,0.015821008,-0.05265896,0.0011958643,-0.03507398,0.009129659,-0.012374746,0.020995153,-0.031242544,0.008703766,-0.03518377,0.050434437,-0.011605142,-0.009010945,0.009854316,0.025380528,0.01360591,0.0722693,-0.034586597,0.04904822,-0.0016005096,-0.061912563,-0.05588042,-0.011354987,-0.050064124,-0.008476018,-0.018802777,-0.0006264877,0.048777185,-0.065281235,-0.042637464,-0.030626033,0.041772116,-0.011245424,-0.012403517,0.004580982,0.032468718,-0.019207362,0.02375148,0.005286878,-0.011482239,0.017616032,-0.009692455,-0.009187722,0.012298897,0.013565668,-0.006341788,0.0038784808,-0.023475353,-0.0061737956,-0.027294012,-0.01667111,-0.038617495,0.025830379,-0.02268131,-0.008553994,-0.027727216,-0.041862052,-0.026544118,0.07502776,-0.031142408,-0.042563617,0.06327213,-0.0772678,-0.093044,0.06414328,0.008275958,0.053743195,0.040236037,-0.015129618,0.041077156,-0.059439346,0.0044424166,0.041611638,-0.00082928565,0.038311806,-0.013742401,-0.027245896,-0.08768225,0.028062884,0.010483919,-0.084709905,-0.035189286,-0.030764803,0.004860814,0.004995015,-0.014870174,0.0027075035,-0.02508683,0.004817203,-0.0054028737,-0.00850092,0.002704835,0.03429663,0.03261561,-0.0029444755,0.031710695,0.012348796,-0.010250752,0.022660568,-0.0068358905,0.015339039,0.021939598,0.042262703,0.029253878,0.0021212439,0.00037342517,-0.03369294,-0.08204644,0.015303024,0.02299918,-0.026447743,0.06671029,0.014130274,0.008382242,-0.008959408,-0.005133722,-0.0020355107,-0.04894091,-0.045656774,-0.017549606,0.059509058,0.032138083,0.08238435,0.034315914,-0.05229893,-0.0044191605,-0.008566409,0.013797001,0.031303477,-0.01000002,0.033988137,0.023640051,-0.012486904,0.011595845,0.06961719,-0.03101907,0.026748959,-0.035163306,0.030707419,0.018146383,0.03143125,-0.024116589,-0.0067817834,0.029579645,-0.06574688,0.019421732,0.015082277,0.022145264,-0.05905905,0.028101563,0.0039308304,-0.05693976,-0.027944682,0.017500428,-0.031676877,0.006902776,0.010190714,-0.040763132,-0.038296398,-0.004409596,0.008705302,0.021371454,-0.01469051,0.028655028,-0.005874382,-0.008831419,-0.07832125,0.011518963,0.012236667,0.017690526,0.010245038,-0.022363564,0.017569968,0.031218365,0.03068855,0.0052697826,-0.1022269,0.04407209,0.017740076,-0.026471125,-0.054009248,-0.00090398313,0.064784005,-0.018417997]}]}", "genkit:state": "success"}, "displayName": "googleai/text-embedding-004", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "c3e3997bfacc89c0": {"spanId": "c3e3997bfacc89c0", "traceId": "3e22700e2a5041dddfebed9779668f19", "startTime": 1752317869699, "endTime": 1752317870291.4395, "attributes": {"genkit:type": "action", "genkit:metadata:subtype": "retriever", "genkit:name": "therapiesRetriever", "genkit:isRoot": true, "genkit:path": "/{therapies<PERSON><PERSON><PERSON><PERSON>,t:action}", "genkit:input": "{\"query\":{\"content\":[{\"text\":\"\\n      User mood score: 3/10\\n      Areas of concern: relationships, social life\\n      Additional information: \\n      Help the user find a the therapy type for their needs.\\n      REQUIREMENTS: Only use data from the CONTEXT passed in.\"}]},\"options\":{\"limit\":3,\"prerankK\":10}}", "genkit:output": "{\"documents\":[{\"content\":[{\"text\":\"Coaching supports individuals, teams or groups in achieving greater self-awareness, improved self-management skills and increased self-efficacy, so that you can develop your own goals and solutions. It is a collaborative, conversation-based process, which emphasises and builds on your existing and developing strengths. It is often focused on supporting you in making changes, either to how things are at present or to your near and distant future.\\n\\nSessions may be quite structured and directional or interactive, and can last up to three hours. Coaching may follow a specific model, but many coaches integrate more than one model, along with elements of therapeutic approaches such as person-centred, solution focused or CBT.\"}],\"metadata\":{\"id\":\"dQjz6M498Gfq9gv6QMrz\",\"name\":\"Coaching\"}},{\"content\":[{\"text\":\"CBT aims to help you change the way you think (cognitive) and what you do (behaviour). Rather than looking at past causes, it focuses on current problems and practical solutions to help you feel better now.\\n\\nThe way we think about situations affects the way we feel and behave. If we view a situation negatively, we may experience negative emotions and feelings which lead us to behave in an unhelpful way. Your therapist will help you identify and challenge any negative thinking so you can deal with situations better and behave in a more positive way\\n\\nCBT can be helpful for depression, anxiety, stress, phobias, obsessions, eating disorders and managing long term conditions.\"}],\"metadata\":{\"id\":\"kMF0pc6JzTKlWsWqKYQE\",\"name\":\"Cognitive behavioural therapy (CBT)\"}},{\"content\":[{\"text\":\"Dialectical Behavior Therapy (DBT) is a type of therapy that helps individuals manage intense emotions and develop healthier coping mechanisms. It's particularly effective for those who struggle with emotional regulation or have difficulty with interpersonal relationships. Originally developed to treat borderline personality disorder, DBT has been adapted to address a range of mental health conditions, including mood disorders, eating disorders, and substance use disorders. \\nHere's a more detailed breakdown:\\nWhat is it?\\nDBT is a structured form of psychotherapy, often described as a talking therapy, that combines cognitive-behavioral techniques with mindfulness practices. \\nIt aims to help individuals accept their current situation while also working towards positive behavioral changes. \\nThe \\\"dialectical\\\" aspect of DBT emphasizes the balance between acceptance and change, acknowledging that both are necessary for progress. \\nKey Components and Skills:\\nMindfulness:\\nLearning to be present in the moment and observe thoughts and feelings without judgment. \\nInterpersonal Effectiveness:\\nDeveloping skills to communicate needs assertively and build healthy relationships. \\nEmotional Regulation:\\nIdentifying, understanding, and managing intense emotions. \\nDistress Tolerance:\\nLearning to cope with difficult situations and intense emotions without resorting to harmful behaviors. \\nWho can benefit? \\nIndividuals with borderline personality disorder.\\nPeople who experience intense emotions and struggle with emotional regulation.\\nThose who engage in self-harming behaviors or have suicidal thoughts.\\nIndividuals with mood disorders, eating disorders, and substance use disorders.\\nIn essence, DBT equips individuals with practical skills to: \\nManage intense emotions and reduce impulsive behaviors.\\nImprove communication and build stronger relationships.\\nDevelop a more positive sense of self and a life worth living.\"}],\"metadata\":{\"id\":\"Z3njnLQp82Jurdsp3ECZ\",\"name\":\"Dialectical Behavior Therapy (DBT)\"}}]}", "genkit:state": "success"}, "displayName": "therapiesRetriever", "links": [], "instrumentationLibrary": {"name": "genkit-tracer", "version": "v1"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": [{"time": 1752317869966.008, "annotation": {"attributes": {"transactional": false, "retry_query_with_cursor": false}, "description": "<PERSON><PERSON><PERSON><PERSON>"}}, {"time": 1752317869971.2456, "annotation": {"attributes": {}, "description": "Firestore.runQuery: Start"}}, {"time": 1752317870205.7441, "annotation": {"attributes": {}, "description": "Firestore.runQuery: First response received"}}]}}}, "displayName": "therapiesRetriever", "startTime": 1752317869699, "endTime": 1752317870291.4395}