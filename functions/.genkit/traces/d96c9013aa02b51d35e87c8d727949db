{"traceId": "d96c9013aa02b51d35e87c8d727949db", "displayName": "CollectionReference.Add", "startTime": 1752163529533, "endTime": 1752163529539.8508, "spans": {"2d371d7dfdaccd48": {"spanId": "2d371d7dfdaccd48", "traceId": "d96c9013aa02b51d35e87c8d727949db", "startTime": 1752163529533, "endTime": 1752163529539.8508, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "CollectionReference.Add", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 2, "message": "Value for argument \"data\" is not a valid Firestore document. Couldn't serialize object of type \"Document\" (found in field \"results.therapies.`0`\"). Firestore doesn't support JavaScript objects with custom prototypes (i.e. objects that were created via the \"new\" operator)."}, "timeEvents": {"timeEvent": [{"time": 1752163529539.839, "annotation": {"attributes": {"exception.type": "Error", "exception.message": "Value for argument \"data\" is not a valid Firestore document. Couldn't serialize object of type \"Document\" (found in field \"results.therapies.`0`\"). Firestore doesn't support JavaScript objects with custom prototypes (i.e. objects that were created via the \"new\" operator).", "exception.stacktrace": "Error: Value for argument \"data\" is not a valid Firestore document. Couldn't serialize object of type \"Document\" (found in field \"results.therapies.`0`\"). Firestore doesn't support JavaScript objects with custom prototypes (i.e. objects that were created via the \"new\" operator).\n    at validateUserInput (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/serializer.js:441:15)\n    at validateUserInput (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/serializer.js:369:13)\n    at validateUserInput (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/serializer.js:375:13)\n    at validateUserInput (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/serializer.js:375:13)\n    at validateDocumentData (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/write-batch.js:609:40)\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/reference/collection-reference.js:218:52\n    at /Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at AsyncLocalStorage.run (node:async_hooks:346:14)\n    at AsyncLocalStorageContextManager.with (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js:33:40)\n    at ContextAPI.with (/Users/<USER>/Documents/dev/neuro/mvp/functions/node_modules/@opentelemetry/api/build/src/api/context.js:60:46)"}, "description": "exception"}}]}}}}