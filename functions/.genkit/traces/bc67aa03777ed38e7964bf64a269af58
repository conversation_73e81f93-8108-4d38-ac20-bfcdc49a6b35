{"traceId": "bc67aa03777ed38e7964bf64a269af58", "spans": {"9a6c96678464be48": {"spanId": "9a6c96678464be48", "traceId": "bc67aa03777ed38e7964bf64a269af58", "parentSpanId": "4c5c0b2e3a4f5548", "startTime": 1752228207038, "endTime": 1752228207249.891, "attributes": {"transactional": false, "doc_count": 1, "otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "Batch.Commit", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "4c5c0b2e3a4f5548": {"spanId": "4c5c0b2e3a4f5548", "traceId": "bc67aa03777ed38e7964bf64a269af58", "parentSpanId": "b19c6b730c586fcd", "startTime": 1752228207038, "endTime": 1752228207252.3953, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "DocumentReference.Create", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "b19c6b730c586fcd": {"spanId": "b19c6b730c586fcd", "traceId": "bc67aa03777ed38e7964bf64a269af58", "startTime": 1752228207037, "endTime": 1752228207253.2732, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "CollectionReference.Add", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "CollectionReference.Add", "startTime": 1752228207037, "endTime": 1752228207253.2732}