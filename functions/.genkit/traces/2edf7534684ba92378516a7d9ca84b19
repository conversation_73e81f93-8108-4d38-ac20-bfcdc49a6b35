{"traceId": "2edf7534684ba92378516a7d9ca84b19", "spans": {"fa4f13c387674326": {"spanId": "fa4f13c387674326", "traceId": "2edf7534684ba92378516a7d9ca84b19", "parentSpanId": "901480e9ee0cd0cd", "startTime": 1752245065763, "endTime": 1752245066007.9875, "attributes": {"transactional": false, "doc_count": 1, "otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "Batch.Commit", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "901480e9ee0cd0cd": {"spanId": "901480e9ee0cd0cd", "traceId": "2edf7534684ba92378516a7d9ca84b19", "parentSpanId": "63b4be52d72ecba6", "startTime": 1752245065761, "endTime": 1752245066009.8572, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "DocumentReference.Create", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}, "63b4be52d72ecba6": {"spanId": "63b4be52d72ecba6", "traceId": "2edf7534684ba92378516a7d9ca84b19", "startTime": 1752245065754, "endTime": 1752245066010.5293, "attributes": {"otel.scope.name": "@google-cloud/firestore", "otel.scope.version": "7.11.0", "gcp.firestore.settings.project_id": "mood-rings-beta", "gcp.firestore.settings.database_id": "(default)", "gcp.firestore.settings.host": "firestore.googleapis.com:443", "gcp.firestore.settings.max_idle_channels": 1, "gcp.firestore.settings.initial_retry_delay": "0.1s", "gcp.firestore.settings.initial_rpc_timeout": "60s", "gcp.firestore.settings.total_timeout": "600s", "gcp.firestore.settings.max_retry_delay": "60s", "gcp.firestore.settings.max_rpc_timeout": "60s", "gcp.firestore.settings.retry_delay_multiplier": "1.3", "gcp.firestore.settings.rpc_timeout_multiplier": "1"}, "displayName": "CollectionReference.Add", "links": [], "instrumentationLibrary": {"name": "@google-cloud/firestore", "version": "7.11.0"}, "spanKind": "INTERNAL", "sameProcessAsParentSpan": {"value": true}, "status": {"code": 0}, "timeEvents": {"timeEvent": []}}}, "displayName": "CollectionReference.Add", "startTime": 1752245065754, "endTime": 1752245066010.5293}