import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { db } from '../Firebase';
import { doc, getDoc } from 'firebase/firestore';
import Markdown from 'react-markdown';
import { TherapyDoc } from '../demo/MoodAssessment';

const TherapyView = () => {
  const { id } = useParams();
  const [therapy, setTherapy] = useState<TherapyDoc | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTherapy = async () => {
      if (!id) {
        setError('No therapy ID provided');
        setLoading(false);
        return;
      }

      try {
        const docRef = doc(db, 'therapies', id);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = docSnap.data();
          setTherapy({
            content: [{ text: data.description || 'No description available' }],
            metadata: {
              id: docSnap.id,
              name: data.name || 'Unknown Therapy',
              ...data
            }
          });
        } else {
          setError('Therapy not found');
        }
      } catch (err: any) {
        setError(`Failed to load therapy: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchTherapy();
  }, [id]);

  if (loading) {
    return (
      <div className="max-w-[80%] mx-auto p-4">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
          Loading therapy information...
        </div>
      </div>
    );
  }

  if (error || !therapy) {
    return (
      <div className="max-w-[80%] mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          {error || 'Failed to load therapy information'}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[80%] mx-auto p-4">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">{therapy.metadata.name}</h1>
        
        <div className="prose lg:prose-xl max-w-full">
          <Markdown>{therapy.content[0].text}</Markdown>
        </div>
        
        <div className="mt-8">
          <a 
            href="/demo" 
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-center border border-gray-400 rounded-lg bg-teal-400 shadow-xl text-gray-800 hover:text-gray-900 hover:bg-teal-600"
          >
            Take Assessment
          </a>
        </div>
      </div>
    </div>
  );
};

export default TherapyView;