import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../Firebase';

interface QuestionSet {
  title: string;
  description: string;
  questions: string[];
  scores: number[];
}

interface ConditionClassificationProps {
  resultId?: string;
  onCompletionChange?: (percentage: number) => void;
}

const ConditionClassification = ({ resultId, onCompletionChange }: ConditionClassificationProps) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('depression');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [totalScore, setTotalScore] = useState(0);
  
  // Initialize question sets with their respective questions and default scores
  const [questionSets, setQuestionSets] = useState<{ [key: string]: QuestionSet }>({
    depression: {
      title: 'Depression (PHQ-9)',
      description: 'Over the last 2 weeks, how often have you been bothered by any of the following problems?',
      questions: [
        'Little interest or pleasure in doing things.',
        'Feeling down, depressed, or hopeless.',
        'Trouble falling or staying asleep, or sleeping too much.',
        'Feeling tired or having little energy.',
        'Poor appetite or overeating.',
        'Feeling bad about yourself, or that you are a failure or have let yourself or your family down.',
        'Trouble concentrating on things, such as reading the newspaper or watching television.',
        'Moving or speaking so slowly that other people could have noticed. Or the opposite, being so fidgety or restless that you have been moving around a lot more than usual.',
        'Thoughts that you would be better off dead, or of hurting yourself in some way.'
      ],
      scores: Array(9).fill(0)
    },
    anxiety: {
      title: 'Anxiety (GAD-7)',
      description: 'Over the last 2 weeks, how often have you been bothered by any of the following problems?',
      questions: [
        'Feeling nervous, anxious, or on edge.',
        'Not being able to stop or control worrying.',
        'Worrying too much about different things.',
        'Trouble relaxing.',
        'Being so restless that it\'s hard to sit still.',
        'Becoming easily annoyed or irritable.',
        'Feeling afraid as if something awful might happen.'
      ],
      scores: Array(7).fill(0)
    },
    psychosis: {
      title: 'Psychosis Screening (PANNS-5)',
      description: 'Over the last month, how often have you experienced any of the following?',
      questions: [
        'Have you experienced things that others could not see or hear, or believed things that others said were not true (e.g., hearing voices or feeling watched)?',
        'Have you felt emotionally numb, withdrawn from others, or found it hard to start or finish everyday tasks?',
        'Have you had trouble organizing your thoughts or speaking in a way others could easily follow?',
        'Have you felt down, anxious, or hopeless?',
        'Have you felt unusually irritable, suspicious of others, or acted aggressively?'
      ],
      scores: Array(5).fill(0)
    }
  });

  // Handle score change for a specific question in the active question set
  const handleScoreChange = (index: number, value: number) => {
    setQuestionSets(prev => {
      const newQuestionSets = { ...prev };
      newQuestionSets[activeTab].scores[index] = value;
      
      // Recalculate total score
      const newTotalScore = calculateTotalScore(activeTab);
      // Remove this line or replace with appropriate action
      
      return newQuestionSets;
    });
  };

  // Calculate total score for a question set
  const calculateTotalScore = (tabKey: string) => {
    return questionSets[tabKey].scores.reduce((sum, score) => sum + score, 0);
  };

  // Get interpretation based on score
  const getInterpretation = (tabKey: string, score: number) => {
    if (tabKey === 'depression') {
      if (score <= 4) return 'Minimal depression';
      if (score <= 9) return 'Mild depression';
      if (score <= 14) return 'Moderate depression';
      if (score <= 19) return 'Moderately severe depression';
      return 'Severe depression';
    } else if (tabKey === 'anxiety') {
      if (score <= 4) return 'Minimal anxiety';
      if (score <= 9) return 'Mild anxiety';
      if (score <= 14) return 'Moderate anxiety';
      return 'Severe anxiety';
    } else if (tabKey === 'psychosis') {
      if (score <= 5) return 'Low risk';
      if (score <= 10) return 'Moderate risk';
      return 'High risk - further assessment recommended';
    }
    return '';
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Here you would typically send the data to your backend
      // For now, we'll just simulate a successful submission
      
      // Calculate scores for all tabs
      const depressionScore = calculateTotalScore('depression');
      const anxietyScore = calculateTotalScore('anxiety');
      const psychosisScore = calculateTotalScore('psychosis');
      
      // Create a results object
      const results = {
        depression: {
          score: depressionScore,
          interpretation: getInterpretation('depression', depressionScore),
          responses: questionSets.depression.scores
        },
        anxiety: {
          score: anxietyScore,
          interpretation: getInterpretation('anxiety', anxietyScore),
          responses: questionSets.anxiety.scores
        },
        psychosis: {
          score: psychosisScore,
          interpretation: getInterpretation('psychosis', psychosisScore),
          responses: questionSets.psychosis.scores
        },
        timestamp: new Date().toISOString()
      };
      
      console.log('Assessment results:', results);
      
      // Here you would save the results to your database
      // and then redirect to a results page
      
      // For now, we'll just alert the user
      alert('Assessment submitted successfully!');
      
    } catch (error) {
      console.error('Error submitting assessment:', error);
      alert('Failed to submit assessment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate and update completion percentage
  useEffect(() => {
    const calculateCompletion = () => {
      let answeredQuestions = 0;
      let totalQuestions = 0;
      
      // Count answered questions across all tabs
      Object.values(questionSets).forEach(set => {
        totalQuestions += set.questions.length;
        set.scores.forEach(score => {
          if (score !== null && score !== undefined) {
            answeredQuestions++;
          }
        });
      });
      
      const percentage = Math.round((answeredQuestions / totalQuestions) * 100);
      
      // Propagate completion percentage to parent component
      if (onCompletionChange) {
        onCompletionChange(percentage);
      }
      
      return percentage;
    };
    
    calculateCompletion();
  }, [questionSets, onCompletionChange]);
  
  // Save scores to Firestore when they change
  useEffect(() => {
    const saveScores = async () => {
      if (!resultId) return;
      
      try {
        const resultRef = doc(db, 'results', resultId);
        const resultDoc = await getDoc(resultRef);
        
        if (resultDoc.exists()) {
          await updateDoc(resultRef, {
            'classification': {
              depression: questionSets.depression.scores,
              anxiety: questionSets.anxiety.scores,
              ptsd: questionSets.ptsd.scores,
              totalScore: calculateTotalScore(activeTab),
              timestamp: new Date()
            }
          });
        }
      } catch (error) {
        console.error('Error saving classification scores:', error);
      }
    };
    
    // Debounce the save operation to avoid too many writes
    const timeoutId = setTimeout(saveScores, 1000);
    return () => clearTimeout(timeoutId);
  }, [questionSets, resultId]);

  return (
    <div className="max-w-[80%] mx-auto p-4">
      <div className="mb-4 text-gray-800 p-2">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6">
          ⚠ This is a screening tool only and not a diagnostic instrument. If you are in crisis or having thoughts of harming yourself, please seek emergency help immediately.
        </div>

        <h2 className='text-3xl font-bold mb-4 text-gray-700'>
          Mental Health Assessment
        </h2>
      </div>

      {/* Tab navigation */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('depression')}
              className={`${
                activeTab === 'depression'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Depression (PHQ-9)
            </button>
            <button
              onClick={() => setActiveTab('anxiety')}
              className={`${
                activeTab === 'anxiety'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Anxiety (GAD-7)
            </button>
            <button
              onClick={() => setActiveTab('psychosis')}
              className={`${
                activeTab === 'psychosis'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Psychosis (PANNS-5)
            </button>
          </nav>
        </div>

        {/* Active tab content */}
        <div className="py-6">
          <h3 className="text-xl font-semibold mb-2">{questionSets[activeTab].title}</h3>
          <p className="mb-6 text-gray-600">{questionSets[activeTab].description}</p>
          
          <div className="mb-4 grid grid-cols-5 gap-4 text-sm font-medium text-gray-500 border-b border-gray-200 pb-2">
            <div className="col-span-4 text-left">Question</div>
            <div className="text-center">Response</div>
          </div>
          
          {questionSets[activeTab].questions.map((question, index) => (
            <div key={index} className="mb-6 border-b pb-4">
              <div className="mb-2">
                <p className="text-gray-700">{index + 1}. {question}</p>
              </div>
              <div className="grid grid-cols-5 gap-4 items-center">
                <div className="col-span-4">
                  <input
                    type="range"
                    id={`${activeTab}-q${index}`}
                    name={`${activeTab}-q${index}`}
                    value={questionSets[activeTab].scores[index]}
                    onChange={(e) => handleScoreChange(index, parseInt(e.target.value))}
                    min="-1"
                    placeholder='-1'
                    max="3"
                    step="1"
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
                <div className="text-center font-medium">
                  {questionSets[activeTab].scores[index] === 0 && "Never"}
                  {questionSets[activeTab].scores[index] === 1 && "Sometimes"}
                  {questionSets[activeTab].scores[index] === 2 && "Often"}
                  {questionSets[activeTab].scores[index] === 3 && "Always"}
                </div>
              </div>
            </div>
          ))}
          
          <div className="mt-8 p-4 bg-gray-100 rounded-lg">
            <p className="font-semibold">Current Score: {calculateTotalScore(activeTab)}</p>
            <p>Interpretation: {getInterpretation(activeTab, calculateTotalScore(activeTab))}</p>
          </div>
          
          <div className="mt-8 flex justify-between">
            <button
              onClick={() => {
                const tabs = ['depression', 'anxiety', 'psychosis'];
                const currentIndex = tabs.indexOf(activeTab);
                if (currentIndex > 0) {
                  setActiveTab(tabs[currentIndex - 1]);
                }
              }}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
              disabled={activeTab === 'depression'}
            >
              Previous
            </button>
            
            {activeTab !== 'psychosis' ? (
              <button
                onClick={() => {
                  const tabs = ['depression', 'anxiety', 'psychosis'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex < tabs.length - 1) {
                    setActiveTab(tabs[currentIndex + 1]);
                  }
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Assessment'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConditionClassification;
